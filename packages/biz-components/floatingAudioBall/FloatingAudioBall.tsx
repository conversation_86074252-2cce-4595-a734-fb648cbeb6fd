import { X } from '@bookln/icon-lucide';
import { Music, Pause, Play } from '@bookln/iconsax';
import { useWindowDimensions } from '@jgl/biz-func';
import { useCallback, useMemo, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  interpolate,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import type { FloatingAudioBallProps } from './types';

const BALL_SIZE = 44;
const BALL_RADIUS = BALL_SIZE / 2;
const ANIMATION_DURATION = 300;
const MARGIN_X = 0;
const INITIAL_MARGIN_BOTTOM = 24;

export const FloatingAudioBall = (props: FloatingAudioBallProps) => {
  const { audioInfo, onHide } = props;

  const [isPlaying, setIsPlaying] = useState(true);

  const { width: screenWidth, height: screenHeight } = useWindowDimensions();

  const { top: safeTop, bottom: safeBottom } = useSafeAreaInsets();

  const isExpanded = useSharedValue(0);
  const translateX = useSharedValue(screenWidth - BALL_SIZE - MARGIN_X);
  const translateY = useSharedValue(
    screenHeight - safeTop - safeBottom - INITIAL_MARGIN_BOTTOM,
  );
  const context = useSharedValue({ x: 0, y: 0 });
  const isDragging = useSharedValue(false);
  const attachedSide = useSharedValue<'left' | 'right'>('right');

  const expandedWidth = useMemo(() => {
    return screenWidth * 0.8;
  }, [screenWidth]);

  const expand = useCallback(() => {
    isExpanded.value = withTiming(1, {
      duration: ANIMATION_DURATION,
    });

    // 展开时调整位置
    if (!isDragging.value) {
      const currentWidth = expandedWidth;

      if (attachedSide.value === 'right') {
        // 吸附在右边时，展开向左延伸，保持右边缘不动
        translateX.value = withTiming(screenWidth - currentWidth - MARGIN_X, {
          duration: ANIMATION_DURATION,
        });
      } else {
        // 吸附在左边时，展开向右延伸，保持左边缘不动
        translateX.value = withTiming(MARGIN_X, {
          duration: ANIMATION_DURATION,
        });
      }
    }
  }, [
    isExpanded,
    isDragging,
    expandedWidth,
    screenWidth,
    translateX,
    attachedSide,
  ]);

  const collapse = useCallback(() => {
    isExpanded.value = withTiming(0, {
      duration: ANIMATION_DURATION,
    });

    // 收起时调整位置
    if (!isDragging.value) {
      const currentWidth = BALL_SIZE;

      if (attachedSide.value === 'right') {
        // 吸附在右边时，收起保持右边缘不动
        translateX.value = withTiming(screenWidth - currentWidth - MARGIN_X, {
          duration: ANIMATION_DURATION,
        });
      } else {
        // 吸附在左边时，收起保持左边缘不动
        translateX.value = withTiming(MARGIN_X, {
          duration: ANIMATION_DURATION,
        });
      }
    }
  }, [isExpanded, isDragging, screenWidth, translateX, attachedSide]);

  const toggleExpand = useCallback(() => {
    const isCurrentlyExpanded = isExpanded.value === 1;
    if (isCurrentlyExpanded) {
      collapse();
    } else {
      expand();
    }
  }, [isExpanded, expand, collapse]);

  const panGesture = useMemo(() => {
    return Gesture.Pan()
      .onStart(() => {
        context.value = { x: translateX.value, y: translateY.value };
        isDragging.value = true; // 开始拖拽时设为圆形
      })
      .onUpdate((event) => {
        translateX.value = context.value.x + event.translationX;
        translateY.value = context.value.y + event.translationY;
      })
      .onEnd(() => {
        isDragging.value = false; // 结束拖拽
        const currentWidth = interpolate(
          isExpanded.value,
          [0, 1],
          [BALL_SIZE, expandedWidth],
        );
        // 基于悬浮球中心点判断吸附方向
        const ballCenterX = translateX.value + currentWidth / 2;
        if (ballCenterX > screenWidth / 2) {
          // 中心点在屏幕右半边，吸附到右边
          attachedSide.value = 'right';
          translateX.value = withTiming(screenWidth - currentWidth - MARGIN_X, {
            duration: ANIMATION_DURATION,
          });
        } else {
          // 中心点在屏幕左半边，吸附到左边
          attachedSide.value = 'left';
          translateX.value = withTiming(MARGIN_X, {
            duration: ANIMATION_DURATION,
          });
        }
      });
  }, [
    context,
    expandedWidth,
    isExpanded,
    screenWidth,
    translateX,
    translateY,
    isDragging,
    attachedSide,
  ]);

  const tapGesture = useMemo(() => {
    return Gesture.Tap().onEnd(() => {
      runOnJS(toggleExpand)();
    });
  }, [toggleExpand]);

  /** 组合手势：如果拖拽失败（即被识别为点击），则执行点击手势 */
  const composedGesture = useMemo(() => {
    return Gesture.Race(panGesture, tapGesture);
  }, [panGesture, tapGesture]);

  const animatedStyle = useAnimatedStyle(() => {
    const width = interpolate(
      isExpanded.value,
      [0, 1],
      [BALL_SIZE, expandedWidth],
    );

    // 计算动态borderRadius - 使用四个角分别设置
    let targetTopLeftRadius = BALL_RADIUS;
    let targetTopRightRadius = BALL_RADIUS;
    let targetBottomLeftRadius = BALL_RADIUS;
    let targetBottomRightRadius = BALL_RADIUS;

    // 如果正在拖拽，保持圆形
    if (!isDragging.value) {
      // 吸边时的borderRadius计算
      const isNearLeftEdge = translateX.value < 20; // 靠近左边缘
      const isNearRightEdge = translateX.value > screenWidth - width - 20; // 靠近右边缘

      if (isNearLeftEdge) {
        // 靠近左边缘：左上角和左下角为0
        targetTopLeftRadius = 0;
        targetBottomLeftRadius = 0;
      } else if (isNearRightEdge) {
        // 靠近右边缘：右上角和右下角为0
        targetTopRightRadius = 0;
        targetBottomRightRadius = 0;
      }
    }

    // 使用withTiming添加平滑过渡动画
    const borderTopLeftRadius = withTiming(targetTopLeftRadius, {
      duration: 200,
    });
    const borderTopRightRadius = withTiming(targetTopRightRadius, {
      duration: 200,
    });
    const borderBottomLeftRadius = withTiming(targetBottomLeftRadius, {
      duration: 200,
    });
    const borderBottomRightRadius = withTiming(targetBottomRightRadius, {
      duration: 200,
    });

    return {
      width,
      borderTopLeftRadius,
      borderTopRightRadius,
      borderBottomLeftRadius,
      borderBottomRightRadius,
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
      ],
    };
  }, [expandedWidth, screenWidth, screenHeight]);

  // 收起状态的内容动画
  const collapsedContentStyle = useAnimatedStyle(() => {
    const opacity = interpolate(isExpanded.value, [0, 0.3, 1], [1, 0, 0]);
    const scale = interpolate(isExpanded.value, [0, 1], [1, 0.8]);

    return {
      opacity,
      transform: [{ scale }],
    };
  });

  // 展开状态的内容动画
  const expandedContentStyle = useAnimatedStyle(() => {
    const opacity = interpolate(isExpanded.value, [0, 0.7, 1], [0, 0, 1]);
    const translateX = interpolate(isExpanded.value, [0, 1], [20, 0]);

    return {
      opacity,
      transform: [{ translateX }],
    };
  });

  const handleClose = useCallback(() => {
    onHide?.();
  }, [onHide]);

  return (
    <GestureDetector gesture={composedGesture}>
      <Animated.View style={[styles.container, animatedStyle]}>
        {/* 收起状态 */}
        <Animated.View
          style={[
            styles.content,
            styles.collapsedContent,
            collapsedContentStyle,
          ]}
        >
          <Music variant={'Bold'} color={'#FF6AA6'} size={24} />
        </Animated.View>

        {/* 展开状态 */}
        <Animated.View
          style={[styles.content, styles.expandedContent, expandedContentStyle]}
        >
          <Text style={styles.title} numberOfLines={1}>
            {audioInfo?.title}
          </Text>
          <View style={styles.controls}>
            <TouchableOpacity onPress={() => setIsPlaying((p) => !p)}>
              {isPlaying ? (
                <Pause variant={'Outline'} />
              ) : (
                <Play variant={'Outline'} />
              )}
            </TouchableOpacity>
            <TouchableOpacity onPress={handleClose}>
              <X />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </Animated.View>
    </GestureDetector>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    height: BALL_SIZE,
    backgroundColor: 'white',
    shadowColor: 'black',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
    elevation: 5,
  },
  content: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  collapsedContent: {},
  expandedContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
  },
  title: {
    flex: 1,
    color: '#171717',
    fontSize: 14,
  },
  controls: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
});
